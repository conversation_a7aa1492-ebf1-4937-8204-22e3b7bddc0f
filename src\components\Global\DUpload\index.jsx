import React, { useEffect, useRef, useState } from "react";

import DButton from "../DButton";
import clsx from "clsx";
import { Transition } from "@headlessui/react";
import UploadArrowIcon from "../Icons/UploadArrowIcon";

const DUpload = React.forwardRef(
  (
    {
      title,
      subtitle,
      note,
      onChangeFile,
      multiple = false,
      accept,
      name,
      id,
      btnClassName,
      className,
    },
    ref
  ) => {
    const [dragActive, setDragActive] = useState(false);

    const refUpload = useRef(ref);
    const dropZoneRef = useRef(null);

    useEffect(() => {
      const dropZone = dropZoneRef.current;
      if (!dropZone) return;

      const handleDragEnter = (e) => {
        e.preventDefault();
        e.stopPropagation();
        setDragActive(true);
      };

      const handleDragOver = (e) => {
        e.preventDefault();
        e.stopPropagation();
        setDragActive(true);
      };

      const handleDragLeave = (e) => {
        e.preventDefault();
        e.stopPropagation();
        // Only set dragActive to false if we're leaving the drop zone entirely
        if (!dropZone.contains(e.relatedTarget)) {
          setDragActive(false);
        }
      };

      const handleDropEvent = (e) => {
        e.preventDefault();
        e.stopPropagation();
        setDragActive(false);

        if (e.dataTransfer.files && e.dataTransfer.files.length > 0) {
          onChangeFile({
            target: {
              files: e.dataTransfer.files,
            },
          });
        }
      };

      dropZone.addEventListener("dragenter", handleDragEnter);
      dropZone.addEventListener("dragover", handleDragOver);
      dropZone.addEventListener("dragleave", handleDragLeave);
      dropZone.addEventListener("drop", handleDropEvent);

      return () => {
        dropZone.removeEventListener("dragenter", handleDragEnter);
        dropZone.removeEventListener("dragover", handleDragOver);
        dropZone.removeEventListener("dragleave", handleDragLeave);
        dropZone.removeEventListener("drop", handleDropEvent);
      };
    }, [onChangeFile]);

    return (
      <div
        ref={dropZoneRef}
        onClick={() => refUpload?.current.click()}
        className={clsx(
          "w-full px-10 py-12 border border-grey-10 rounded-[12px] flex flex-col justify-center items-center text-center gap-3 relative overflow-hidden bg-white dark:bg-[#212121] cursor-pointer hover:bg-grey-5 dark:hover:bg-grey-10 transition-colors",
          {
            "border-dashed border-grey-20": dragActive,
          },
          className
        )}
      >
        <Transition show={dragActive}>
          <div className="transition-all duration-300 absolute inset-0 bg-white/80 dark:bg-[#212121]/80 flex justify-center items-center">
            <div className="flex flex-col justify-center items-center text-center gap-size1">
              <span className="text-base font-medium tracking-tight">
                {multiple ? "Drop files here" : "Drop file here"}
              </span>
            </div>
          </div>
        </Transition>

        <UploadArrowIcon />
        <span className="text-base font-medium">
          {multiple ? "Upload or drop files" : "Upload or drop"}
        </span>

        <input
          ref={refUpload}
          type="file"
          name={name}
          id={id}
          className="hidden"
          onChange={onChangeFile}
          multiple={multiple}
          accept={accept}
        />
      </div>
    );
  }
);

DUpload.displayName = "DUpload";

export default DUpload;
