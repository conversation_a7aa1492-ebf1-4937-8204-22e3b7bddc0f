// Example usage of the updated DContentUpload component
// This shows how to use the new header logo upload styling pattern

import { useState } from 'react';
import DContentUpload from '@/components/Global/DContentUpload';

const ExampleUploadComponent = () => {
  const [contentUrl, setContentUrl] = useState('');
  const [contentFile, setContentFile] = useState(null);
  const [error, setError] = useState('');

  const handleFileChange = (file) => {
    setContentFile(file);
    setContentUrl(''); // Clear URL when file is selected
    setError(''); // Clear any errors
  };

  const handleFileRemove = () => {
    setContentFile(null);
    setError('');
  };

  const handleUrlChange = (e) => {
    setContentUrl(e.target.value);
    setContentFile(null); // Clear file when URL is entered
    setError('');
  };

  return (
    <div className="p-6 max-w-md">
      <h2 className="text-xl font-semibold mb-4">Upload Content</h2>
      
      {/* Example 1: Image and Video Upload with Preview and Remove */}
      <DContentUpload
        label="Media Content"
        urlPlaceholder="Enter image or video URL"
        urlValue={contentUrl}
        onUrlChange={handleUrlChange}
        onFileChange={handleFileChange}
        fileValue={contentFile} // Shows preview of uploaded file
        onFileRemove={handleFileRemove} // Shows remove button
        acceptedFileTypes="image/*,video/*"
        maxFileSize="50MB"
        supportedFormats="Images: PNG, JPEG, JPG, GIF, SVG • Videos: MP4, MOV, AVI, WebM"
        error={error}
      />

      {/* Example 2: Image Only Upload */}
      <div className="mt-6">
        <DContentUpload
          label="Profile Picture"
          urlPlaceholder="Enter image URL"
          urlValue={contentUrl}
          onUrlChange={handleUrlChange}
          onFileChange={handleFileChange}
          fileValue={contentFile}
          onFileRemove={handleFileRemove}
          acceptedFileTypes="image/*"
          maxFileSize="10MB"
          supportedFormats="Images: PNG, JPEG, JPG, GIF, SVG"
          error={error}
        />
      </div>

      {/* Example 3: Video Only Upload */}
      <div className="mt-6">
        <DContentUpload
          label="Video Content"
          urlPlaceholder="Enter video URL"
          urlValue={contentUrl}
          onUrlChange={handleUrlChange}
          onFileChange={handleFileChange}
          fileValue={contentFile}
          onFileRemove={handleFileRemove}
          acceptedFileTypes="video/*"
          maxFileSize="100MB"
          supportedFormats="Videos: MP4, MOV, AVI, WebM"
          error={error}
        />
      </div>

      {/* Example 4: Without File Preview (backward compatibility) */}
      <div className="mt-6">
        <DContentUpload
          label="Simple Upload"
          urlPlaceholder="Enter content URL"
          urlValue={contentUrl}
          onUrlChange={handleUrlChange}
          onFileChange={handleFileChange}
          // No fileValue or onFileRemove props - works like before
          acceptedFileTypes="image/*,video/*"
          maxFileSize="50MB"
          supportedFormats="Images: PNG, JPEG, JPG, GIF, SVG • Videos: MP4, MOV, AVI, WebM"
          error={error}
        />
      </div>
    </div>
  );
};

export default ExampleUploadComponent;
