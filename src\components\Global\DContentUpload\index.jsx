import { useState, useRef } from 'react';
import DInput from '../DInput/DInput';
import LinkIcon from '../Icons/LinkIcon';
import UploadIcon from '../Icons/UploadIcon';
import CameraIcon from '../Icons/CameraIcon';
import clsx from 'clsx';

const DContentUpload = ({
  label,
  urlPlaceholder = "Enter URL",
  urlValue,
  onUrlChange,
  onFileChange,
  acceptedFileTypes = "image/*,video/*",
  maxFileSize = "50MB",
  supportedFormats = "Images: PNG, JPEG, JPG, GIF, SVG • Videos: MP4, MOV, AVI, WebM",
  urlIcon = <LinkIcon />,
  error,
  className = "",
  showTabs = true,
  defaultTab = "url", // "url" or "file"
  fileValue, // Add support for displaying current file
  onFileRemove, // Add support for removing file
  ...props
}) => {
  const [activeTab, setActiveTab] = useState(defaultTab);
  const fileInputRef = useRef(null);

  const handleFileUpload = (e) => {
    const files = e.target.files;
    if (files && files.length > 0) {
      onFileChange?.(files[0]);
    }
  };

  const handleFileRemove = (e) => {
    e.stopPropagation();
    onFileRemove?.();
    if (fileInputRef.current) {
      fileInputRef.current.value = '';
    }
  };

  const getFilePreviewUrl = () => {
    if (!fileValue) return null;
    if (typeof fileValue === 'string') return fileValue;
    return URL.createObjectURL(fileValue);
  };

  const isVideo = (file) => {
    if (!file) return false;
    if (typeof file === 'string') {
      return file.match(/\.(mp4|mov|avi|webm)$/i);
    }
    return file.type?.startsWith('video/');
  };

  if (!showTabs) {
    // Simple URL input without tabs
    return (
      <div className={`flex flex-col gap-size1 ${className}`}>
        {label && (
          <p className="text-base font-medium tracking-tight">{label}</p>
        )}
        <DInput
          placeholder={urlPlaceholder}
          value={urlValue}
          onChange={onUrlChange}
          icon={urlIcon}
          error={error}
          {...props}
        />
      </div>
    );
  }

  return (
    <div className={`flex flex-col gap-size2 ${className}`}>
      {label && (
        <p className="text-base font-medium tracking-tight">{label}</p>
      )}
      
      {/* Tab Navigation */}
      <div className="flex gap-size1">
        <button
          className={`dbutton flex w-full items-center justify-center rounded-size2 h-9 gap-size0 ${
            activeTab === 'url' ? 'bg-purple-100' : 'bg-grey-1'
          }`}
          onClick={() => setActiveTab('url')}
        >
          <LinkIcon
            className={`${
              activeTab === 'url' ? 'text-purple-300' : 'text-black'
            }`}
          />
          <span
            className={`${
              activeTab === 'url' ? 'text-purple-300' : 'text-black'
            } text-xs`}
          >
            Add URL
          </span>
        </button>
        <button
          className={`dbutton flex w-full items-center justify-center rounded-size2 h-9 gap-size0 ${
            activeTab === 'file' ? 'bg-purple-100' : 'bg-grey-1'
          }`}
          onClick={() => setActiveTab('file')}
        >
          <UploadIcon
            className={`${
              activeTab === 'file' ? 'text-purple-300' : 'text-black'
            }`}
          />
          <span
            className={`${
              activeTab === 'file' ? 'text-purple-300' : 'text-black'
            } text-xs`}
          >
            Upload File
          </span>
        </button>
      </div>

      {/* Tab Content */}
      <div className="min-h-[120px]">
        {activeTab === 'url' && (
          <div className="flex flex-col gap-size1">
            <DInput
              placeholder={urlPlaceholder}
              value={urlValue}
              onChange={onUrlChange}
              icon={urlIcon}
              error={error}
              {...props}
            />
          </div>
        )}
        
        {activeTab === 'file' && (
          <div className="flex flex-col gap-size1">
            <div
              className={clsx(
                'flex items-center justify-center gap-size1 border-dashed border h-20 w-full rounded-size2 p-size1 cursor-pointer relative',
                {
                  'border-black-10': !error,
                  'border-red-500': error,
                }
              )}
              onClick={() => fileInputRef.current?.click()}
            >
              {fileValue ? (
                <>
                  {isVideo(fileValue) ? (
                    <video
                      src={getFilePreviewUrl()}
                      className="h-full w-full object-contain rounded-size2"
                      controls={false}
                      muted
                    />
                  ) : (
                    <img
                      src={getFilePreviewUrl()}
                      alt="File Preview"
                      className="h-full w-full object-contain rounded-size2"
                    />
                  )}
                  {onFileRemove && (
                    <button
                      className="!size-6 !absolute right-[-5px] bottom-[-5px] !z-10 text-grey-50 bg-white !p-[2px] !rounded-full border border-grey-10 hover:bg-grey-5 transition-colors"
                      onClick={handleFileRemove}
                      type="button"
                    >
                      <svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <path d="M12 4L4 12M4 4l8 8" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"/>
                      </svg>
                    </button>
                  )}
                </>
              ) : (
                <CameraIcon />
              )}
            </div>
            <input
              type="file"
              className="hidden"
              onChange={handleFileUpload}
              ref={fileInputRef}
              accept={acceptedFileTypes}
            />
            <p className="text-grey-20 text-xs font-light">
              {supportedFormats} • Max. size {maxFileSize}
            </p>
          </div>
        )}
      </div>
    </div>
  );
};

export default DContentUpload;
